#!/usr/bin/env python3
"""
API使用示例
演示如何调用Coser场照分析API
"""
import requests
import json
import os

def analyze_coser_photo_example():
    """
    演示如何调用分析API
    注意：这个示例需要一个真实的图片文件和配置好的GLM API密钥
    """
    
    # API端点
    url = "http://localhost:8000/api/v1/analyze"
    
    # 示例数据
    data = {
        "character_name": "初音未来",
        "user_info": "希望生成一个在樱花飞舞的春日校园场景中的prompt"
    }
    
    # 注意：这里需要一个真实的图片文件
    # files = {
    #     "image": ("coser_photo.jpg", open("path/to/your/image.jpg", "rb"), "image/jpeg")
    # }
    
    print("📝 API调用示例:")
    print(f"URL: {url}")
    print(f"数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
    print("\n💡 要实际调用API，请:")
    print("1. 确保后端服务正在运行")
    print("2. 在.env文件中配置ZHIPUAI_API_KEY")
    print("3. 准备一张coser场照图片")
    print("4. 取消注释上面的files部分并指定正确的图片路径")
    
    # 示例调用代码（注释掉，因为需要真实图片）
    """
    try:
        response = requests.post(url, data=data, files=files)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 分析成功!")
            print(f"结果: {result['result']}")
        else:
            print(f"❌ 分析失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    finally:
        if 'files' in locals():
            files['image'][1].close()
    """

def curl_example():
    """生成curl命令示例"""
    print("\n🌐 Curl命令示例:")
    print("""
curl -X POST "http://localhost:8000/api/v1/analyze" \\
     -H "Content-Type: multipart/form-data" \\
     -F "image=@/path/to/your/coser_photo.jpg" \\
     -F "character_name=初音未来" \\
     -F "user_info=希望生成一个在樱花飞舞的春日校园场景中的prompt"
    """)

def main():
    """主函数"""
    print("🎭 Coser场照分析API使用示例")
    print("=" * 50)
    
    # Python示例
    analyze_coser_photo_example()
    
    # Curl示例
    curl_example()
    
    print("\n📚 更多信息:")
    print("- API文档: http://localhost:8000/docs")
    print("- 健康检查: http://localhost:8000/api/v1/health")
    print("- 项目README: ../README.md")

if __name__ == "__main__":
    main()
