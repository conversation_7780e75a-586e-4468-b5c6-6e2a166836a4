#!/usr/bin/env python3
"""
测试API脚本
"""
import requests
import json

def test_health_check():
    """测试健康检查端点"""
    try:
        response = requests.get("http://localhost:8000/api/v1/health")
        print(f"健康检查状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_root_endpoint():
    """测试根端点"""
    try:
        response = requests.get("http://localhost:8000/api/v1/")
        print(f"根端点状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"根端点测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试Coser分析API...")
    
    # 测试健康检查
    print("\n1. 测试健康检查端点...")
    health_ok = test_health_check()
    
    # 测试根端点
    print("\n2. 测试根端点...")
    root_ok = test_root_endpoint()
    
    # 总结
    print("\n📊 测试结果:")
    print(f"✅ 健康检查: {'通过' if health_ok else '失败'}")
    print(f"✅ 根端点: {'通过' if root_ok else '失败'}")
    
    if health_ok and root_ok:
        print("\n🎉 所有基础测试通过！API服务正常运行。")
        print("💡 提示：要测试图像分析功能，需要配置GLM API密钥。")
    else:
        print("\n❌ 部分测试失败，请检查服务状态。")

if __name__ == "__main__":
    main()
