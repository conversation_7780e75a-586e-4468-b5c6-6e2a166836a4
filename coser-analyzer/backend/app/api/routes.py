from fastapi import APIRouter, UploadFile, File, Form, HTTPException
from fastapi.responses import JSONResponse
from ..crew.crew import CoserAnalyzerCrew
from ..utils.file_handler import FileHandler
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()
file_handler = FileHandler()

@router.post("/analyze")
async def analyze_coser_photo(
    image: UploadFile = File(..., description="Coser场照图片"),
    character_name: str = Form(..., description="Cosplay角色名称"),
    user_info: str = Form("", description="用户补充信息")
):
    """
    分析coser场照并生成报告
    
    Args:
        image: 上传的图片文件
        character_name: 角色名称
        user_info: 用户补充信息（可选）
        
    Returns:
        分析结果的JSON响应
    """
    file_path = None
    
    try:
        logger.info(f"开始分析coser场照，角色: {character_name}")
        
        # 保存上传的文件
        file_path = await file_handler.save_upload_file(image)
        logger.info(f"文件已保存到: {file_path}")
        
        # 创建分析crew
        crew = CoserAnalyzerCrew()
        
        # 执行分析
        result = crew.analyze_coser_photo(
            image_path=file_path,
            character_name=character_name,
            user_info=user_info
        )
        
        logger.info("分析完成")
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "result": result,
                "character_name": character_name,
                "user_info": user_info
            }
        )
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"分析过程中发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"分析失败: {str(e)}"
        )
    finally:
        # 清理临时文件
        if file_path:
            file_handler.cleanup_file(file_path)

@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "message": "Coser分析服务运行正常"}

@router.get("/")
async def root():
    """根端点"""
    return {
        "message": "Coser场照分析API",
        "version": "1.0.0",
        "endpoints": {
            "analyze": "POST /analyze - 分析coser场照",
            "health": "GET /health - 健康检查"
        }
    }
