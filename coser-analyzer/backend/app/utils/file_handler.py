import os
import uuid
from pathlib import Path
from PIL import Image
import aiofiles
from fastapi import UploadFile, HTTPException

class FileHandler:
    """文件处理工具类"""
    
    def __init__(self, upload_dir: str = "uploads", max_size: int = 10485760):
        self.upload_dir = Path(upload_dir)
        self.max_size = max_size
        self.allowed_extensions = {'jpg', 'jpeg', 'png', 'webp'}
        
        # 创建上传目录
        self.upload_dir.mkdir(exist_ok=True)
    
    def _get_file_extension(self, filename: str) -> str:
        """获取文件扩展名"""
        return filename.split('.')[-1].lower() if '.' in filename else ''
    
    def _is_allowed_file(self, filename: str) -> bool:
        """检查文件类型是否允许"""
        extension = self._get_file_extension(filename)
        return extension in self.allowed_extensions
    
    def _validate_image(self, file_path: Path) -> bool:
        """验证图片文件"""
        try:
            with Image.open(file_path) as img:
                img.verify()
            return True
        except Exception:
            return False
    
    async def save_upload_file(self, upload_file: UploadFile) -> str:
        """
        保存上传的文件
        
        Args:
            upload_file: FastAPI上传文件对象
            
        Returns:
            保存的文件路径
            
        Raises:
            HTTPException: 文件验证失败时抛出异常
        """
        # 检查文件大小
        if upload_file.size and upload_file.size > self.max_size:
            raise HTTPException(
                status_code=413,
                detail=f"文件大小超过限制 ({self.max_size} bytes)"
            )
        
        # 检查文件类型
        if not self._is_allowed_file(upload_file.filename):
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件类型。支持的类型: {', '.join(self.allowed_extensions)}"
            )
        
        # 生成唯一文件名
        file_extension = self._get_file_extension(upload_file.filename)
        unique_filename = f"{uuid.uuid4()}.{file_extension}"
        file_path = self.upload_dir / unique_filename
        
        # 保存文件
        try:
            async with aiofiles.open(file_path, 'wb') as f:
                content = await upload_file.read()
                await f.write(content)
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"文件保存失败: {str(e)}"
            )
        
        # 验证图片
        if not self._validate_image(file_path):
            # 删除无效文件
            file_path.unlink(missing_ok=True)
            raise HTTPException(
                status_code=400,
                detail="无效的图片文件"
            )
        
        return str(file_path)
    
    def cleanup_file(self, file_path: str):
        """清理临时文件"""
        try:
            Path(file_path).unlink(missing_ok=True)
        except Exception:
            pass  # 忽略清理错误
