"""
GLM模型配置工具
"""
import os
from langchain_openai import ChatOpenAI
from dotenv import load_dotenv

# 加载环境变量 - 从项目根目录
import pathlib
project_root = pathlib.Path(__file__).parent.parent.parent.parent
load_dotenv(dotenv_path=project_root / ".env")

def create_glm_llm(model_name="glm-4", temperature=0.7):
    """
    创建GLM模型实例
    
    Args:
        model_name: 模型名称，支持 glm-4, glm-4v
        temperature: 温度参数
        
    Returns:
        ChatOpenAI实例
    """
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if not api_key:
        raise ValueError("ZHIPUAI_API_KEY 环境变量未设置")
    
    # GLM API配置
    config = {
        "model": model_name,
        "openai_api_key": api_key,
        "openai_api_base": "https://open.bigmodel.cn/api/paas/v4/",
        "temperature": temperature,
        "max_retries": 3,
        "request_timeout": 120,
        "max_tokens": 2000 if model_name == "glm-4v" else 1500
    }
    
    return ChatOpenAI(**config)

def test_glm_connection():
    """
    测试GLM连接
    """
    try:
        llm = create_glm_llm()
        response = llm.invoke("你好，请回复'连接成功'")
        return True, response.content
    except Exception as e:
        return False, str(e)
