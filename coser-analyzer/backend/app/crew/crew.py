from .agents import ImageAnalystAgent, ContentGeneratorAgent

class CoserAnalyzerCrew:
    """Coser场照分析Crew"""

    def __init__(self):
        self.image_analyst = ImageAnalystAgent()
        self.content_generator = ContentGeneratorAgent()

    def analyze_coser_photo(self, image_path: str, character_name: str, user_info: str = ""):
        """
        分析coser场照并生成完整报告

        Args:
            image_path: 图片文件路径
            character_name: 角色名称
            user_info: 用户补充信息

        Returns:
            分析结果字符串
        """
        try:
            # 执行图像分析
            image_result = self.image_analyst.analyze_image(image_path)

            # 执行内容生成
            final_result = self.content_generator.generate_content(
                character_name=character_name,
                user_info=user_info if user_info else "无特殊要求",
                image_analysis=image_result
            )

            return final_result

        except Exception as e:
            raise Exception(f"分析过程失败: {str(e)}")
