import os
import base64
from crewai import Agent
from langchain_openai import ChatOpenAI

def create_image_analyst_agent():
    """创建图像分析专家代理"""
    # 使用GLM模型
    llm = ChatOpenAI(
        temperature=0.7,
        model="glm-4v",
        openai_api_key=os.getenv("ZHIPUAI_API_KEY"),
        openai_api_base="https://open.bigmodel.cn/api/paas/v4/",
    )

    return Agent(
        role="Coser场照分析专家",
        goal="对coser场照进行专业的多维度分析，提供详细的视觉描述",
        backstory="""你是一位专业的coser场照分析专家，拥有丰富的二次元文化知识和摄影分析经验。
        你擅长从多个专业角度分析coser的表演，包括动作姿态、道具使用、服装细节、
        化妆技巧和摄影光线等方面。你的分析准确、详细且具有专业性。""",
        verbose=True,
        llm=llm,
        allow_delegation=False
    )

def create_content_generator_agent():
    """创建内容生成专家代理"""
    # 使用GLM模型
    llm = ChatOpenAI(
        temperature=0.8,
        model="glm-4",
        openai_api_key=os.getenv("ZHIPUAI_API_KEY"),
        openai_api_base="https://open.bigmodel.cn/api/paas/v4/",
    )

    return Agent(
        role="二次元角色Coser场照分析助手",
        goal="整合coser场照分析信息和角色设定，为文生图应用生成高质量的prompt提示词",
        backstory="""你是一个专业的二次元角色助手，擅长整合coser场照分析信息和角色设定，
        为文生图应用生成高质量的prompt提示词。你精通二次元文化，了解各种角色设定，
        能够根据用户需求设计新的背景环境和前景特效，生成适合的文生图prompt。
        你总是优先满足用户的个性化需求，同时保持角色的核心特征。""",
        verbose=True,
        llm=llm,
        allow_delegation=False
    )
