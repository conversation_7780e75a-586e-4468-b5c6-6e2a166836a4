import openai
import base64
import os
from typing import Dict, Any

class ImageAnalystAgent:
    """图像分析专家代理"""

    def __init__(self):
        self.client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.role = "Coser场照分析专家"
        self.backstory = """你是一位专业的coser场照分析专家，拥有丰富的二次元文化知识和摄影分析经验。
        你擅长从多个专业角度分析coser的表演，包括动作姿态、道具使用、服装细节、
        化妆技巧和摄影光线等方面。你的分析准确、详细且具有专业性。"""

    def analyze_image(self, image_path: str) -> str:
        """分析图像"""
        try:
            # 读取并编码图像
            with open(image_path, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')

            response = self.client.chat.completions.create(
                model="gpt-4-vision-preview",
                messages=[
                    {
                        "role": "system",
                        "content": f"你是{self.role}。{self.backstory}"
                    },
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": """请对这张coser场照进行详细分析，从以下几个方面进行专业描述：

1. **动作姿态**：识别coser的具体动作（如"跳舞"、"战斗姿势"、"胜利手势"、"微笑挥手"等）
2. **武器道具**：识别手持或佩戴的道具（如"拿着剑"、"手持法杖"、"背着弓箭"、"戴着头饰"等）
3. **服饰细节**：分析服装特征（如"穿着校服"、"战斗装"、"和服"、"哥特洛丽塔"等）
4. **发型发色**：识别coser的发型和发色特征（如"长发"、"短发"、"金色长发"、"黄色长发"等）
5. **妆容分析**：观察化妆风格（如"淡妆"、"华丽妆容"、"舞台妆"、"特效妆"等）
6. **光线氛围**：分析打光效果（如"柔光"、"戏剧性光影"、"彩色灯光"等）

请提供详细、准确的分析结果，每个方面都要有具体的描述。"""
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=1000
            )

            return response.choices[0].message.content

        except Exception as e:
            raise Exception(f"图像分析失败: {str(e)}")

class ContentGeneratorAgent:
    """内容生成专家代理"""

    def __init__(self):
        self.client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.role = "二次元角色Coser场照分析助手"
        self.backstory = """你是一个专业的二次元角色助手，擅长整合coser场照分析信息和角色设定，
        为文生图应用生成高质量的prompt提示词。你精通二次元文化，了解各种角色设定，
        能够根据用户需求设计新的背景环境和前景特效，生成适合的文生图prompt。
        你总是优先满足用户的个性化需求，同时保持角色的核心特征。"""

    def generate_content(self, character_name: str, user_info: str, image_analysis: str) -> str:
        """生成内容"""
        try:
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {
                        "role": "system",
                        "content": f"你是{self.role}。{self.backstory}"
                    },
                    {
                        "role": "user",
                        "content": f"""根据以下信息，生成一个完整的二次元角色Coser场照分析报告：

**角色名称：** {character_name}
**用户补充信息：** {user_info}
**Coser场照分析信息：** {image_analysis}

请按照以下格式生成完整的markdown文档，严格遵循用户需求优先的原则：

1. 首先进行输入信息确认
2. 然后进行角色信息分析
3. 接着进行信息整合分析
4. 最后提供三个不同的Prompt提示词方案

请确保：
- 用户补充信息具有最高优先级
- 保持角色核心特征
- 有效利用coser场照分析信息
- 提供中英文两种prompt
- 内容健康向上，符合积极价值观"""
                    }
                ],
                max_tokens=2000
            )

            return response.choices[0].message.content

        except Exception as e:
            raise Exception(f"内容生成失败: {str(e)}")
