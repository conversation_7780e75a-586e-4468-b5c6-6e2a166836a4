# 使用指南

## 🎯 一键操作

### 快速启动
```bash
# 在项目根目录下
./start.sh
```

### 停止服务
```bash
./stop.sh
```

### 安装依赖
```bash
./install.sh
```

## 📁 项目结构

```
coser-analyzer/
├── 📜 start.sh              # 一键启动脚本
├── 📜 install.sh            # 一键安装脚本  
├── 📜 stop.sh               # 停止服务脚本
├── 📖 README.md             # 项目说明
├── 📖 USAGE.md              # 使用指南
├── 📊 PROJECT_STATUS.md     # 项目状态
├── backend/                 # 后端服务
│   ├── app/
│   │   ├── crew/           # CrewAI代理系统
│   │   │   ├── agents.py   # AI代理定义
│   │   │   ├── tasks.py    # 任务定义
│   │   │   └── crew.py     # 代理协作
│   │   ├── api/
│   │   │   └── routes.py   # API路由
│   │   ├── utils/
│   │   │   └── file_handler.py # 文件处理
│   │   └── main.py         # FastAPI应用
│   ├── coser_analyzer_env/ # Python虚拟环境
│   ├── requirements.txt    # Python依赖
│   ├── .env               # 环境配置
│   ├── test_api.py        # API测试
│   └── api_example.py     # 使用示例
└── frontend/               # 前端应用
    ├── src/
    │   ├── components/     # React组件
    │   ├── styles/        # 样式文件
    │   └── App.jsx        # 主应用
    ├── package.json       # Node.js依赖
    ├── start_frontend.sh  # 前端启动脚本
    └── simple_test.html   # 简单测试页面
```

## 🔧 配置说明

### 后端配置 (backend/.env)
```bash
# GLM API配置
ZHIPUAI_API_KEY=your_api_key_here
GLM_API_BASE=https://open.bigmodel.cn/api/paas/v4/
GLM_MODEL=glm-4v

# 服务配置
HOST=0.0.0.0
PORT=8000
DEBUG=True

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,webp
```

### 代理配置
如果网络访问有问题，脚本会自动设置代理：
```bash
export http_proxy=http://127.0.0.1:7890
export https_proxy=http://127.0.0.1:7890
```

## 🎭 使用流程

### 1. 启动应用
```bash
./start.sh
```

### 2. 访问前端
浏览器自动打开 `http://localhost:3000`

### 3. 上传分析
1. **上传图片**: 拖拽或选择coser场照
2. **填写信息**: 
   - 角色名称 (必填): 如"初音未来"、"鸣人-六道模式"
   - 补充信息 (可选): 如"樱花校园场景"、"夜晚城市背景"
3. **开始分析**: 点击按钮等待AI处理
4. **查看结果**: 获得专业的markdown分析报告

### 4. 结果内容
分析报告包含：
- 📋 输入信息确认
- 🎭 角色信息分析  
- 🔄 信息整合分析
- 🎨 三种Prompt方案 (中英文)

## 🛠️ 故障排除

### 后端问题
```bash
# 检查后端状态
curl http://localhost:8000/api/v1/health

# 查看后端日志
cd backend
source coser_analyzer_env/bin/activate
python -m app.main

# 测试API
python test_api.py
```

### 前端问题
```bash
# 检查Node.js版本
nvm list
nvm use v20.19.0

# 重新安装依赖
cd frontend
rm -rf node_modules
npm install

# 手动启动
./start_frontend.sh
```

### 网络问题
```bash
# 设置代理
export http_proxy=http://127.0.0.1:7890
export https_proxy=http://127.0.0.1:7890

# 测试网络
curl -I https://www.google.com
```

### GLM API问题
1. 检查API密钥是否正确
2. 确认账户余额充足
3. 验证API访问权限

## 📊 性能说明

- **图像分析**: 通常需要10-30秒
- **内容生成**: 通常需要15-45秒  
- **总处理时间**: 约1-2分钟
- **支持格式**: JPG, PNG, WEBP
- **文件大小**: 最大10MB

## 🔒 安全说明

- API密钥存储在本地.env文件中
- 上传的图片仅临时存储，分析完成后自动删除
- 所有通信使用HTTPS加密
- 不会保存用户的个人信息

## 📞 技术支持

- **API文档**: http://localhost:8000/docs
- **项目文档**: README.md
- **状态报告**: PROJECT_STATUS.md
- **测试工具**: backend/test_api.py
