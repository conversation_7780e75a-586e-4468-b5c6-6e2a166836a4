# Coser场照分析器

一个使用CrewAI技术的智能coser场照分析系统，能够从多个维度分析coser表演，并生成专业的文生图prompt。

## 🎯 项目状态

✅ **后端完成**: 使用CrewAI + GLM-4V + FastAPI构建的智能分析服务
⏳ **前端待完成**: React + Vite + Tailwind CSS现代化界面
🔧 **当前可用**: API服务已启动，支持图像分析和内容生成

## 功能特点

- 🎭 **多维度分析**: 从动作姿态、武器道具、服饰细节、发型发色、妆容分析、光线氛围等6个方面分析coser场照
- 🤖 **AI驱动**: 使用CrewAI多代理协作框架，结合GLM-4V多模态大语言模型进行图像理解
- 📝 **智能生成**: 根据角色设定和用户需求，生成三种不同风格的文生图prompt
- 🎨 **现代界面**: 基于React + Tailwind CSS的优雅现代前端界面（开发中）
- 📱 **响应式设计**: 支持桌面和移动设备访问

## 技术栈

### 后端 ✅
- **FastAPI**: 现代Python Web框架
- **CrewAI 0.1.32**: AI代理协作框架
- **GLM-4V**: 智谱AI多模态大语言模型
- **LangChain**: 大语言模型应用框架
- **Pillow**: 图像处理库

### 前端 ⏳
- **React 18**: 现代前端框架
- **Vite**: 快速构建工具
- **Tailwind CSS**: 实用优先的CSS框架
- **React Markdown**: Markdown渲染组件
- **React Dropzone**: 文件拖拽上传组件

## 🚀 快速开始

### 环境要求

- Python 3.9+
- 智谱AI API Key (GLM-4V)
- Node.js 16+ (前端开发需要)

### 当前可用功能

✅ **后端API服务** - 已完成并运行中

### 安装步骤

#### 1. 后端设置 ✅

```bash
# 进入后端目录
cd coser-analyzer/backend

# 创建虚拟环境
python3 -m venv coser_analyzer_env

# 激活虚拟环境
source coser_analyzer_env/bin/activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
# 编辑 .env 文件，添加您的智谱AI API Key
ZHIPUAI_API_KEY=your_zhipuai_api_key_here
GLM_API_BASE=https://open.bigmodel.cn/api/paas/v4/
GLM_MODEL=glm-4v

# 启动后端服务
python -m app.main
```

#### 2. 前端设置 ⏳ (待完成)

```bash
# 需要先安装Node.js
cd ../frontend
npm install
npm run dev
```

### 🧪 测试API

后端服务启动后，可以运行测试脚本验证：

```bash
# 在backend目录下
python test_api.py
```

### 📡 API端点

- **健康检查**: `GET http://localhost:8000/api/v1/health`
- **根端点**: `GET http://localhost:8000/api/v1/`
- **分析接口**: `POST http://localhost:8000/api/v1/analyze`
- **API文档**: `http://localhost:8000/docs`

## 使用说明

1. **上传图片**: 拖拽或点击上传coser场照（支持JPG、PNG、WEBP格式，最大10MB）
2. **输入角色信息**: 填写cosplay的角色名称（如：初音未来、鸣人-六道模式等）
3. **添加补充信息**: 可选填写个性化需求，如背景要求、特效需求等
4. **开始分析**: 点击"开始分析"按钮，AI将进行多维度分析
5. **查看结果**: 获得包含角色分析和三种prompt方案的完整markdown报告
6. **导出结果**: 可复制或下载分析结果

## API文档

启动后端服务后，访问 `http://localhost:8000/docs` 查看完整的API文档。

### 主要端点

- `POST /api/v1/analyze`: 分析coser场照
- `GET /api/v1/health`: 健康检查

## 项目结构

```
coser-analyzer/
├── backend/                 # 后端代码
│   ├── app/
│   │   ├── crew/           # CrewAI代理和任务
│   │   ├── api/            # API路由
│   │   ├── utils/          # 工具函数
│   │   └── main.py         # 应用入口
│   ├── requirements.txt    # Python依赖
│   └── .env               # 环境变量
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── components/     # React组件
│   │   ├── styles/         # 样式文件
│   │   └── App.jsx         # 主应用组件
│   ├── package.json        # Node.js依赖
│   └── vite.config.js      # Vite配置
└── README.md              # 项目说明
```

## 开发指南

### 添加新的分析维度

1. 在 `backend/app/crew/tasks.py` 中修改图像分析任务描述
2. 更新 `backend/app/crew/agents.py` 中代理的专业背景
3. 调整前端显示逻辑

### 自定义prompt模板

在 `backend/app/crew/tasks.py` 的内容生成任务中修改prompt生成逻辑。

## 注意事项

- 确保OpenAI API Key有足够的额度
- 图像分析可能需要几分钟时间，请耐心等待
- 建议使用高质量的coser场照以获得更好的分析效果

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
