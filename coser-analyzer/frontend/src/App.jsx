import React, { useState } from 'react';
import axios from 'axios';
import { <PERSON><PERSON>les, AlertCircle } from 'lucide-react';
import FileUpload from './components/FileUpload';
import InputForm from './components/InputForm';
import LoadingSpinner from './components/LoadingSpinner';
import ResultDisplay from './components/ResultDisplay';

function App() {
  const [selectedFile, setSelectedFile] = useState(null);
  const [characterName, setCharacterName] = useState('');
  const [userInfo, setUserInfo] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);
  const [loadingStage, setLoadingStage] = useState('analyzing');

  const handleFileSelect = (file) => {
    setSelectedFile(file);
    setError(null);
  };

  const handleFileRemove = () => {
    setSelectedFile(null);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!selectedFile || !characterName.trim()) {
      setError('请上传图片并输入角色名称');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);
    setLoadingStage('analyzing');

    try {
      const formData = new FormData();
      formData.append('image', selectedFile);
      formData.append('character_name', characterName.trim());
      formData.append('user_info', userInfo.trim());

      // 模拟不同阶段
      setTimeout(() => setLoadingStage('processing'), 2000);
      setTimeout(() => setLoadingStage('generating'), 4000);

      const response = await axios.post('/api/v1/analyze', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 300000, // 5分钟超时
      });

      if (response.data.success) {
        setResult(response.data.result);
      } else {
        throw new Error(response.data.error || '分析失败');
      }
    } catch (err) {
      console.error('分析错误:', err);
      if (err.code === 'ECONNABORTED') {
        setError('请求超时，请检查网络连接或稍后重试');
      } else if (err.response?.data?.detail) {
        setError(err.response.data.detail);
      } else {
        setError(err.message || '分析过程中发生错误，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setSelectedFile(null);
    setCharacterName('');
    setUserInfo('');
    setResult(null);
    setError(null);
    setLoading(false);
  };

  return (
    <div className="min-h-screen py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* 头部 */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Sparkles className="w-8 h-8 text-blue-500" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Coser场照分析器
            </h1>
          </div>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            使用AI技术分析coser场照，生成专业的角色分析报告和文生图prompt
          </p>
        </div>

        {/* 主要内容 */}
        {result ? (
          <ResultDisplay result={result} onReset={handleReset} />
        ) : loading ? (
          <LoadingSpinner stage={loadingStage} />
        ) : (
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* 文件上传 */}
            <div className="card">
              <FileUpload
                onFileSelect={handleFileSelect}
                selectedFile={selectedFile}
                onFileRemove={handleFileRemove}
              />
            </div>

            {/* 输入表单 */}
            <div className="card">
              <InputForm
                characterName={characterName}
                setCharacterName={setCharacterName}
                userInfo={userInfo}
                setUserInfo={setUserInfo}
              />
            </div>

            {/* 错误提示 */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start gap-3">
                <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-red-800">分析失败</h4>
                  <p className="text-red-600 mt-1">{error}</p>
                </div>
              </div>
            )}

            {/* 提交按钮 */}
            <div className="text-center">
              <button
                type="submit"
                disabled={!selectedFile || !characterName.trim() || loading}
                className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed text-lg px-8 py-4"
              >
                <Sparkles className="w-5 h-5 mr-2" />
                开始分析
              </button>
            </div>
          </form>
        )}

        {/* 底部说明 */}
        <div className="mt-16 text-center text-gray-500 text-sm">
          <p>
            本工具使用CrewAI技术，结合多模态AI分析coser场照，
            为您生成专业的角色分析和文生图prompt
          </p>
        </div>
      </div>
    </div>
  );
}

export default App;
