@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gradient-to-br from-blue-50 via-white to-purple-50 min-h-screen;
  }
}

@layer components {
  .card {
    @apply bg-white rounded-xl shadow-lg border border-gray-100 p-6;
  }
  
  .btn-primary {
    @apply bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg;
  }
  
  .btn-secondary {
    @apply bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 transition-all duration-200;
  }
  
  .input-field {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
  }
  
  .upload-zone {
    @apply border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 transition-colors duration-200 cursor-pointer;
  }
  
  .upload-zone.active {
    @apply border-blue-500 bg-blue-50;
  }
}
