import React from 'react';
import { User, MessageSquare } from 'lucide-react';

const InputForm = ({ characterName, setCharacterName, userInfo, setUserInfo }) => {
  return (
    <div className="space-y-6">
      <div>
        <label className="block text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
          <User className="w-5 h-5" />
          角色名称
        </label>
        <input
          type="text"
          value={characterName}
          onChange={(e) => setCharacterName(e.target.value)}
          placeholder="请输入cosplay的角色名称，如：初音未来、鸣人-六道模式等"
          className="input-field"
          required
        />
        <p className="text-sm text-gray-500 mt-2">
          可以是具体角色名、特殊形态或角色类型描述
        </p>
      </div>

      <div>
        <label className="block text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
          <MessageSquare className="w-5 h-5" />
          补充信息
          <span className="text-sm font-normal text-gray-500">（可选）</span>
        </label>
        <textarea
          value={userInfo}
          onChange={(e) => setUserInfo(e.target.value)}
          placeholder="请输入您的个性化需求，如背景要求、特效需求、氛围要求等..."
          className="input-field min-h-[120px] resize-y"
          rows={4}
        />
        <p className="text-sm text-gray-500 mt-2">
          您的补充信息将具有最高优先级，用于指导最终的prompt生成
        </p>
      </div>
    </div>
  );
};

export default InputForm;
