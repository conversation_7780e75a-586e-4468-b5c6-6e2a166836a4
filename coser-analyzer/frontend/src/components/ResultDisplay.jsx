import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import { Copy, Download, CheckCircle, FileText, RotateCcw } from 'lucide-react';

const ResultDisplay = ({ result, onReset }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(result);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  const handleDownload = () => {
    const blob = new Blob([result], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `coser-analysis-${Date.now()}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* 操作按钮 */}
      <div className="flex flex-wrap gap-3 justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
          <FileText className="w-6 h-6" />
          分析结果
        </h2>
        
        <div className="flex gap-3">
          <button
            onClick={handleCopy}
            className={`btn-secondary flex items-center gap-2 ${
              copied ? 'bg-green-100 text-green-700' : ''
            }`}
          >
            {copied ? (
              <>
                <CheckCircle className="w-4 h-4" />
                已复制
              </>
            ) : (
              <>
                <Copy className="w-4 h-4" />
                复制
              </>
            )}
          </button>
          
          <button
            onClick={handleDownload}
            className="btn-secondary flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            下载
          </button>
          
          <button
            onClick={onReset}
            className="btn-primary flex items-center gap-2"
          >
            <RotateCcw className="w-4 h-4" />
            重新分析
          </button>
        </div>
      </div>

      {/* Markdown 内容 */}
      <div className="card">
        <div className="prose prose-lg max-w-none">
          <ReactMarkdown
            components={{
              h1: ({ children }) => (
                <h1 className="text-3xl font-bold text-gray-800 mb-4 pb-2 border-b-2 border-blue-200">
                  {children}
                </h1>
              ),
              h2: ({ children }) => (
                <h2 className="text-2xl font-semibold text-gray-700 mt-8 mb-4">
                  {children}
                </h2>
              ),
              h3: ({ children }) => (
                <h3 className="text-xl font-semibold text-gray-700 mt-6 mb-3">
                  {children}
                </h3>
              ),
              h4: ({ children }) => (
                <h4 className="text-lg font-semibold text-gray-600 mt-4 mb-2">
                  {children}
                </h4>
              ),
              p: ({ children }) => (
                <p className="text-gray-600 leading-relaxed mb-4">
                  {children}
                </p>
              ),
              ul: ({ children }) => (
                <ul className="list-disc list-inside space-y-2 mb-4 text-gray-600">
                  {children}
                </ul>
              ),
              ol: ({ children }) => (
                <ol className="list-decimal list-inside space-y-2 mb-4 text-gray-600">
                  {children}
                </ol>
              ),
              li: ({ children }) => (
                <li className="ml-4">{children}</li>
              ),
              code: ({ inline, children }) => (
                inline ? (
                  <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono text-gray-800">
                    {children}
                  </code>
                ) : (
                  <pre className="bg-gray-50 p-4 rounded-lg overflow-x-auto mb-4">
                    <code className="text-sm font-mono text-gray-800">
                      {children}
                    </code>
                  </pre>
                )
              ),
              blockquote: ({ children }) => (
                <blockquote className="border-l-4 border-blue-300 pl-4 italic text-gray-600 my-4">
                  {children}
                </blockquote>
              ),
              strong: ({ children }) => (
                <strong className="font-semibold text-gray-800">{children}</strong>
              ),
            }}
          >
            {result}
          </ReactMarkdown>
        </div>
      </div>
    </div>
  );
};

export default ResultDisplay;
