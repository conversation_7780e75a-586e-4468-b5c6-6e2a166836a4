import React from 'react';
import { Loader2, Brain, Image, FileText } from 'lucide-react';

const LoadingSpinner = ({ stage = 'analyzing' }) => {
  const stages = {
    analyzing: {
      icon: Image,
      title: '正在分析图片...',
      description: 'AI正在从多个维度分析您的coser场照'
    },
    processing: {
      icon: Brain,
      title: '正在处理信息...',
      description: '整合角色设定和场照分析信息'
    },
    generating: {
      icon: FileText,
      title: '正在生成报告...',
      description: '创建专业的markdown分析报告'
    }
  };

  const currentStage = stages[stage];
  const Icon = currentStage.icon;

  return (
    <div className="card text-center py-12">
      <div className="flex flex-col items-center space-y-6">
        {/* 主要加载动画 */}
        <div className="relative">
          <div className="w-20 h-20 border-4 border-blue-200 rounded-full animate-spin-slow"></div>
          <div className="absolute inset-0 w-20 h-20 border-4 border-transparent border-t-blue-500 rounded-full animate-spin"></div>
          <div className="absolute inset-4 w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center">
            <Icon className="w-6 h-6 text-blue-500 animate-pulse-slow" />
          </div>
        </div>

        {/* 状态文本 */}
        <div className="space-y-2">
          <h3 className="text-xl font-semibold text-gray-800">
            {currentStage.title}
          </h3>
          <p className="text-gray-600 max-w-md">
            {currentStage.description}
          </p>
        </div>

        {/* 进度指示器 */}
        <div className="flex space-x-2">
          {Object.keys(stages).map((key, index) => (
            <div
              key={key}
              className={`w-3 h-3 rounded-full transition-all duration-500 ${
                key === stage
                  ? 'bg-blue-500 scale-125'
                  : Object.keys(stages).indexOf(stage) > index
                  ? 'bg-green-500'
                  : 'bg-gray-300'
              }`}
            />
          ))}
        </div>

        {/* 提示文本 */}
        <div className="text-sm text-gray-500 bg-gray-50 rounded-lg p-4 max-w-md">
          <Loader2 className="w-4 h-4 inline mr-2 animate-spin" />
          这可能需要几分钟时间，请耐心等待...
        </div>
      </div>
    </div>
  );
};

export default LoadingSpinner;
