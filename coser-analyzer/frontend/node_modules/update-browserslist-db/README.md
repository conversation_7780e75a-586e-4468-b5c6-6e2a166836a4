# Update Browserslist DB

<img width="120" height="120" alt="Browserslist logo by <PERSON>"
     src="https://browsersl.ist/logo.svg" align="right">

CLI tool to update `caniuse-lite` with browsers DB
from [Browserslist](https://github.com/browserslist/browserslist/) config.

Some queries like `last 2 versions` or `>1%` depend on actual data
from `caniuse-lite`.

```sh
npx update-browserslist-db@latest
```

<a href="https://evilmartians.com/?utm_source=update-browserslist-db">
  <img src="https://evilmartians.com/badges/sponsored-by-evil-martians.svg"
       alt="Sponsored by Evil Martians" width="236" height="54">
</a>

## Docs
Read full docs **[here](https://github.com/browserslist/update-db#readme)**.
