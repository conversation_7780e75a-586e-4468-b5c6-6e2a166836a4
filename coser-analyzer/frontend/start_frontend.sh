#!/bin/bash

echo "🚀 启动前端开发服务器"
echo "======================"

# 设置代理
export http_proxy=http://127.0.0.1:7890
export https_proxy=http://127.0.0.1:7890

# 加载nvm
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

# 使用Node.js v20.19.0
nvm use v20.19.0

# 设置npm代理
npm config set proxy http://127.0.0.1:7890
npm config set https-proxy http://127.0.0.1:7890

echo "📦 Node.js版本: $(node --version)"
echo "📦 npm版本: $(npm --version)"
echo ""

# 启动开发服务器
echo "🌐 启动Vite开发服务器..."
npm run dev
