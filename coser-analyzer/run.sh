#!/bin/bash

# Coser场照分析器 - 简单启动脚本
# 直接启动，不做复杂检测

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${PURPLE}🎭 Coser场照分析器 - 启动中...${NC}"
echo ""

# 设置代理
export http_proxy=http://127.0.0.1:7890
export https_proxy=http://127.0.0.1:7890
echo -e "${GREEN}✅ 代理已设置${NC}"

# 启动后端
echo -e "${BLUE}🚀 启动后端服务...${NC}"
cd backend
source coser_analyzer_env/bin/activate
nohup python -m app.main > ../backend.log 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > ../backend.pid
echo -e "${GREEN}✅ 后端启动中 (PID: $BACKEND_PID)${NC}"
cd ..

# 等待后端启动
echo -e "${YELLOW}⏳ 等待后端启动...${NC}"
sleep 8

# 启动前端
echo -e "${BLUE}🎨 启动前端服务...${NC}"
cd frontend
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
nvm use v20.19.0 > /dev/null 2>&1
nohup npm run dev > ../frontend.log 2>&1 &
FRONTEND_PID=$!
echo $FRONTEND_PID > ../frontend.pid
echo -e "${GREEN}✅ 前端启动中 (PID: $FRONTEND_PID)${NC}"
cd ..

# 等待前端启动
echo -e "${YELLOW}⏳ 等待前端启动...${NC}"
sleep 10

# 打开浏览器
echo -e "${BLUE}🌐 打开浏览器...${NC}"
open http://localhost:3000 2>/dev/null || echo "请手动打开: http://localhost:3000"

echo ""
echo -e "${GREEN}🎉 启动完成！${NC}"
echo ""
echo -e "${BLUE}📊 服务地址:${NC}"
echo "  • 前端应用: http://localhost:3000"
echo "  • 后端API: http://localhost:8000"
echo "  • API文档: http://localhost:8000/docs"
echo ""
echo -e "${BLUE}📝 日志文件:${NC}"
echo "  • 后端日志: backend.log"
echo "  • 前端日志: frontend.log"
echo ""
echo -e "${BLUE}🛑 停止服务:${NC}"
echo "  ./stop.sh"
echo ""
echo -e "${YELLOW}💡 提示: 服务需要几秒钟完全启动，请稍等片刻再使用${NC}"
