#!/bin/bash

# Coser场照分析器 - 安装脚本
# 一键安装所有依赖

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_message() {
    echo -e "${2}${1}${NC}"
}

print_title() {
    echo ""
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}🎭 Coser场照分析器 - 安装脚本${NC}"
    echo -e "${PURPLE}================================${NC}"
    echo ""
}

# 设置代理
setup_proxy() {
    print_message "🌐 设置网络代理..." $BLUE
    export http_proxy=http://127.0.0.1:7890
    export https_proxy=http://127.0.0.1:7890
    print_message "✅ 代理设置完成" $GREEN
}

# 安装后端依赖
install_backend() {
    print_message "🐍 安装后端依赖..." $BLUE
    
    cd backend
    
    # 创建虚拟环境
    if [ ! -d "coser_analyzer_env" ]; then
        print_message "📦 创建Python虚拟环境..." $YELLOW
        python3 -m venv coser_analyzer_env
    fi
    
    # 激活虚拟环境并安装依赖
    source coser_analyzer_env/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    
    print_message "✅ 后端依赖安装完成" $GREEN
    cd ..
}

# 安装前端依赖
install_frontend() {
    print_message "🎨 安装前端依赖..." $BLUE
    
    cd frontend
    
    # 加载nvm
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    
    # 使用Node.js v20.19.0
    nvm use v20.19.0
    
    # 设置npm代理
    npm config set proxy http://127.0.0.1:7890
    npm config set https-proxy http://127.0.0.1:7890
    
    # 安装依赖
    npm install
    
    print_message "✅ 前端依赖安装完成" $GREEN
    cd ..
}

# 检查配置
check_config() {
    print_message "🔧 检查配置..." $BLUE
    
    if [ ! -f "backend/.env" ]; then
        print_message "⚠️  请配置backend/.env文件中的GLM API密钥" $YELLOW
        print_message "   ZHIPUAI_API_KEY=your_api_key_here" $NC
    else
        print_message "✅ 配置文件存在" $GREEN
    fi
}

main() {
    print_title
    setup_proxy
    install_backend
    install_frontend
    check_config
    
    echo ""
    print_message "🎉 安装完成！" $GREEN
    print_message "💡 下一步:" $BLUE
    print_message "  1. 配置backend/.env文件中的GLM API密钥" $NC
    print_message "  2. 运行 ./start.sh 启动服务" $NC
    echo ""
}

main
