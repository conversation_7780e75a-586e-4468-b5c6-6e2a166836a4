#!/bin/bash

# Coser场照分析器 - 一键启动脚本
# 自动启动后端和前端服务

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

# 打印标题
print_title() {
    echo ""
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}🎭 Coser场照分析器 - 一键启动${NC}"
    echo -e "${PURPLE}================================${NC}"
    echo ""
}

# 检查依赖
check_dependencies() {
    print_message "🔍 检查系统依赖..." $BLUE
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        print_message "❌ Python3 未安装" $RED
        exit 1
    fi
    
    # 检查nvm
    if [ ! -s "$HOME/.nvm/nvm.sh" ]; then
        print_message "❌ nvm 未安装" $RED
        exit 1
    fi
    
    print_message "✅ 系统依赖检查通过" $GREEN
}

# 设置代理
setup_proxy() {
    print_message "🌐 设置网络代理..." $BLUE
    export http_proxy=http://127.0.0.1:7890
    export https_proxy=http://127.0.0.1:7890
    print_message "✅ 代理设置完成" $GREEN
}

# 启动后端
start_backend() {
    print_message "🚀 启动后端服务..." $BLUE
    
    cd backend
    
    # 检查虚拟环境
    if [ ! -d "coser_analyzer_env" ]; then
        print_message "❌ 虚拟环境不存在，请先运行安装脚本" $RED
        exit 1
    fi
    
    # 检查.env文件
    if [ ! -f ".env" ]; then
        print_message "❌ .env文件不存在，请配置GLM API密钥" $RED
        exit 1
    fi
    
    # 激活虚拟环境并启动后端
    source coser_analyzer_env/bin/activate
    
    # 检查后端是否已在运行
    if curl -s http://localhost:8000/api/v1/health > /dev/null 2>&1; then
        print_message "⚠️  后端服务已在运行" $YELLOW
    else
        print_message "🔄 启动FastAPI服务器..." $CYAN
        python -m app.main &
        BACKEND_PID=$!
        
        # 等待后端启动
        print_message "⏳ 等待后端服务启动..." $CYAN
        for i in {1..30}; do
            if curl -s http://localhost:8000/api/v1/health > /dev/null 2>&1; then
                print_message "✅ 后端服务启动成功 (PID: $BACKEND_PID)" $GREEN
                break
            fi
            sleep 1
            if [ $i -eq 30 ]; then
                print_message "❌ 后端服务启动超时" $RED
                exit 1
            fi
        done
    fi
    
    cd ..
}

# 启动前端
start_frontend() {
    print_message "🎨 启动前端服务..." $BLUE
    
    cd frontend
    
    # 加载nvm
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
    
    # 使用Node.js v20.19.0
    nvm use v20.19.0 > /dev/null 2>&1
    
    # 设置npm代理
    npm config set proxy http://127.0.0.1:7890 > /dev/null 2>&1
    npm config set https-proxy http://127.0.0.1:7890 > /dev/null 2>&1
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        print_message "📦 安装前端依赖..." $CYAN
        npm install
    fi
    
    # 检查前端是否已在运行
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        print_message "⚠️  前端服务已在运行" $YELLOW
    else
        print_message "🔄 启动Vite开发服务器..." $CYAN
        npm run dev &
        FRONTEND_PID=$!
        
        # 等待前端启动
        print_message "⏳ 等待前端服务启动..." $CYAN
        for i in {1..30}; do
            if curl -s http://localhost:3000 > /dev/null 2>&1; then
                print_message "✅ 前端服务启动成功 (PID: $FRONTEND_PID)" $GREEN
                break
            fi
            sleep 1
            if [ $i -eq 30 ]; then
                print_message "❌ 前端服务启动超时" $RED
                exit 1
            fi
        done
    fi
    
    cd ..
}

# 打开浏览器
open_browser() {
    print_message "🌐 打开浏览器..." $BLUE
    
    # 等待2秒确保服务完全启动
    sleep 2
    
    if command -v open > /dev/null 2>&1; then
        # macOS
        open http://localhost:3000
    elif command -v xdg-open > /dev/null 2>&1; then
        # Linux
        xdg-open http://localhost:3000
    elif command -v start > /dev/null 2>&1; then
        # Windows
        start http://localhost:3000
    else
        print_message "请手动打开浏览器访问: http://localhost:3000" $YELLOW
    fi
    
    print_message "✅ 浏览器已打开" $GREEN
}

# 显示服务信息
show_info() {
    echo ""
    print_message "🎉 所有服务启动完成！" $GREEN
    echo ""
    print_message "📊 服务信息:" $CYAN
    print_message "  • 前端应用: http://localhost:3000" $NC
    print_message "  • 后端API: http://localhost:8000" $NC
    print_message "  • API文档: http://localhost:8000/docs" $NC
    print_message "  • 健康检查: http://localhost:8000/api/v1/health" $NC
    echo ""
    print_message "💡 使用说明:" $CYAN
    print_message "  1. 在浏览器中上传coser场照" $NC
    print_message "  2. 输入角色名称和补充信息" $NC
    print_message "  3. 点击'开始分析'按钮" $NC
    print_message "  4. 等待AI分析完成并查看结果" $NC
    echo ""
    print_message "🛑 停止服务:" $CYAN
    print_message "  按 Ctrl+C 停止所有服务" $NC
    echo ""
}

# 清理函数
cleanup() {
    echo ""
    print_message "🛑 正在停止服务..." $YELLOW
    
    # 停止后台进程
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        print_message "✅ 后端服务已停止" $GREEN
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        print_message "✅ 前端服务已停止" $GREEN
    fi
    
    # 停止可能在运行的服务
    pkill -f "python -m app.main" 2>/dev/null || true
    pkill -f "npm run dev" 2>/dev/null || true
    
    print_message "👋 再见！" $PURPLE
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    print_title
    check_dependencies
    setup_proxy
    start_backend
    start_frontend
    open_browser
    show_info
    
    # 保持脚本运行
    print_message "⏳ 服务正在运行中... (按 Ctrl+C 停止)" $BLUE
    while true; do
        sleep 1
    done
}

# 运行主函数
main
