#!/bin/bash

# Coser场照分析器 - 演示脚本
# 展示项目功能和使用方法

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_message() {
    echo -e "${2}${1}${NC}"
}

print_title() {
    clear
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                                                              ║"
    echo "║               🎭 Coser场照分析器 - 演示                      ║"
    echo "║                                                              ║"
    echo "║          使用CrewAI + GLM-4V的智能分析系统                   ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo ""
}

show_features() {
    print_message "🌟 项目特色功能:" $CYAN
    echo ""
    print_message "  🤖 CrewAI多代理协作" $NC
    print_message "     • 图像分析专家代理" $NC
    print_message "     • 内容生成专家代理" $NC
    echo ""
    print_message "  👁️ GLM-4V多模态AI" $NC
    print_message "     • 智谱AI最新视觉模型" $NC
    print_message "     • 支持图像理解和文本生成" $NC
    echo ""
    print_message "  📊 6维度专业分析" $NC
    print_message "     • 动作姿态 • 武器道具 • 服饰细节" $NC
    print_message "     • 发型发色 • 妆容分析 • 光线氛围" $NC
    echo ""
    print_message "  🎨 现代化界面" $NC
    print_message "     • React + Vite + Tailwind CSS" $NC
    print_message "     • 拖拽上传 • 实时反馈 • 响应式设计" $NC
    echo ""
}

show_architecture() {
    print_message "🏗️ 技术架构:" $CYAN
    echo ""
    print_message "  后端 (Python):" $BLUE
    print_message "    ├── FastAPI (Web框架)" $NC
    print_message "    ├── CrewAI (多代理系统)" $NC
    print_message "    ├── GLM-4V (多模态AI)" $NC
    print_message "    ├── LangChain (AI应用框架)" $NC
    print_message "    └── Pillow (图像处理)" $NC
    echo ""
    print_message "  前端 (JavaScript):" $BLUE
    print_message "    ├── React 18 (UI框架)" $NC
    print_message "    ├── Vite (构建工具)" $NC
    print_message "    ├── Tailwind CSS (样式)" $NC
    print_message "    ├── React Markdown (渲染)" $NC
    print_message "    └── React Dropzone (上传)" $NC
    echo ""
}

show_usage() {
    print_message "🚀 使用方法:" $CYAN
    echo ""
    print_message "  1️⃣ 一键安装:" $GREEN
    print_message "     ./install.sh" $YELLOW
    echo ""
    print_message "  2️⃣ 配置API密钥:" $GREEN
    print_message "     编辑 backend/.env 文件" $YELLOW
    print_message "     ZHIPUAI_API_KEY=your_api_key" $YELLOW
    echo ""
    print_message "  3️⃣ 一键启动:" $GREEN
    print_message "     ./start.sh" $YELLOW
    echo ""
    print_message "  4️⃣ 使用应用:" $GREEN
    print_message "     • 浏览器自动打开 http://localhost:3000" $NC
    print_message "     • 上传coser场照" $NC
    print_message "     • 输入角色信息" $NC
    print_message "     • 获得AI分析报告" $NC
    echo ""
    print_message "  5️⃣ 停止服务:" $GREEN
    print_message "     ./stop.sh" $YELLOW
    echo ""
}

show_demo_flow() {
    print_message "📝 分析流程演示:" $CYAN
    echo ""
    print_message "  输入 → 处理 → 输出" $BLUE
    echo ""
    print_message "  📸 上传图片:" $GREEN
    print_message "     coser_photo.jpg (初音未来cosplay)" $NC
    echo ""
    print_message "  📝 输入信息:" $GREEN
    print_message "     角色名称: 初音未来" $NC
    print_message "     补充信息: 樱花校园场景" $NC
    echo ""
    print_message "  🤖 AI分析过程:" $GREEN
    print_message "     1. 图像分析代理 → 6维度分析" $NC
    print_message "     2. 内容生成代理 → 整合信息" $NC
    print_message "     3. 生成3种prompt方案" $NC
    echo ""
    print_message "  📊 输出结果:" $GREEN
    print_message "     • 详细的markdown分析报告" $NC
    print_message "     • 中英文prompt提示词" $NC
    print_message "     • 可复制、下载的格式" $NC
    echo ""
}

show_files() {
    print_message "📁 项目文件:" $CYAN
    echo ""
    print_message "  核心脚本:" $BLUE
    print_message "    ├── start.sh     (一键启动)" $NC
    print_message "    ├── install.sh   (一键安装)" $NC
    print_message "    ├── stop.sh      (停止服务)" $NC
    print_message "    └── demo.sh      (演示脚本)" $NC
    echo ""
    print_message "  文档文件:" $BLUE
    print_message "    ├── README.md           (项目说明)" $NC
    print_message "    ├── USAGE.md            (使用指南)" $NC
    print_message "    └── PROJECT_STATUS.md   (项目状态)" $NC
    echo ""
    print_message "  服务状态:" $BLUE
    if curl -s http://localhost:8000/api/v1/health > /dev/null 2>&1; then
        print_message "    ├── 后端服务: ✅ 运行中" $GREEN
    else
        print_message "    ├── 后端服务: ❌ 未运行" $RED
    fi
    
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        print_message "    └── 前端服务: ✅ 运行中" $GREEN
    else
        print_message "    └── 前端服务: ❌ 未运行" $RED
    fi
    echo ""
}

interactive_menu() {
    while true; do
        echo ""
        print_message "🎯 选择操作:" $CYAN
        print_message "  1) 查看项目特色" $NC
        print_message "  2) 查看技术架构" $NC
        print_message "  3) 查看使用方法" $NC
        print_message "  4) 查看分析流程" $NC
        print_message "  5) 查看项目文件" $NC
        print_message "  6) 启动应用" $NC
        print_message "  7) 停止应用" $NC
        print_message "  8) 退出演示" $NC
        echo ""
        read -p "请输入选择 (1-8): " choice
        
        case $choice in
            1) clear; print_title; show_features ;;
            2) clear; print_title; show_architecture ;;
            3) clear; print_title; show_usage ;;
            4) clear; print_title; show_demo_flow ;;
            5) clear; print_title; show_files ;;
            6) 
                print_message "🚀 启动应用..." $GREEN
                ./start.sh
                ;;
            7) 
                print_message "🛑 停止应用..." $YELLOW
                ./stop.sh
                ;;
            8) 
                print_message "👋 感谢使用！" $PURPLE
                exit 0
                ;;
            *) 
                print_message "❌ 无效选择，请输入1-8" $RED
                ;;
        esac
        
        echo ""
        read -p "按回车键继续..."
        clear
        print_title
    done
}

main() {
    print_title
    print_message "欢迎使用Coser场照分析器演示系统！" $GREEN
    print_message "这是一个基于CrewAI和GLM-4V的智能分析应用。" $NC
    interactive_menu
}

main
