#!/bin/bash

# Coser场照分析器 - 停止脚本

echo "🛑 停止Coser场照分析器服务..."

# 停止后端服务
if [ -f "backend.pid" ]; then
    BACKEND_PID=$(cat backend.pid)
    kill $BACKEND_PID 2>/dev/null && echo "✅ 后端服务已停止"
    rm -f backend.pid
fi

# 停止前端服务
if [ -f "frontend.pid" ]; then
    FRONTEND_PID=$(cat frontend.pid)
    kill $FRONTEND_PID 2>/dev/null && echo "✅ 前端服务已停止"
    rm -f frontend.pid
fi

# 强制停止相关进程
pkill -f "python -m app.main" 2>/dev/null
pkill -f "npm run dev" 2>/dev/null
pkill -f "vite" 2>/dev/null

# 清理日志文件
rm -f backend.log frontend.log

echo "🎉 所有服务已停止！"
