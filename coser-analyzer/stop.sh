#!/bin/bash

# Coser场照分析器 - 停止脚本
# 停止所有相关服务

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_message() {
    echo -e "${2}${1}${NC}"
}

print_title() {
    echo ""
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}🛑 Coser场照分析器 - 停止服务${NC}"
    echo -e "${PURPLE}================================${NC}"
    echo ""
}

stop_services() {
    print_message "🔍 查找并停止相关服务..." $BLUE
    
    # 停止后端服务
    BACKEND_PIDS=$(pgrep -f "python -m app.main" 2>/dev/null || true)
    if [ ! -z "$BACKEND_PIDS" ]; then
        echo "$BACKEND_PIDS" | xargs kill 2>/dev/null || true
        print_message "✅ 后端服务已停止" $GREEN
    else
        print_message "ℹ️  后端服务未运行" $YELLOW
    fi
    
    # 停止前端服务
    FRONTEND_PIDS=$(pgrep -f "npm run dev" 2>/dev/null || true)
    if [ ! -z "$FRONTEND_PIDS" ]; then
        echo "$FRONTEND_PIDS" | xargs kill 2>/dev/null || true
        print_message "✅ 前端服务已停止" $GREEN
    else
        print_message "ℹ️  前端服务未运行" $YELLOW
    fi
    
    # 停止可能的Vite进程
    VITE_PIDS=$(pgrep -f "vite" 2>/dev/null || true)
    if [ ! -z "$VITE_PIDS" ]; then
        echo "$VITE_PIDS" | xargs kill 2>/dev/null || true
        print_message "✅ Vite服务已停止" $GREEN
    fi
    
    # 停止可能的uvicorn进程
    UVICORN_PIDS=$(pgrep -f "uvicorn" 2>/dev/null || true)
    if [ ! -z "$UVICORN_PIDS" ]; then
        echo "$UVICORN_PIDS" | xargs kill 2>/dev/null || true
        print_message "✅ Uvicorn服务已停止" $GREEN
    fi
}

check_ports() {
    print_message "🔍 检查端口占用..." $BLUE
    
    # 检查8000端口
    if lsof -i :8000 > /dev/null 2>&1; then
        print_message "⚠️  端口8000仍被占用" $YELLOW
        lsof -i :8000
    else
        print_message "✅ 端口8000已释放" $GREEN
    fi
    
    # 检查3000端口
    if lsof -i :3000 > /dev/null 2>&1; then
        print_message "⚠️  端口3000仍被占用" $YELLOW
        lsof -i :3000
    else
        print_message "✅ 端口3000已释放" $GREEN
    fi
}

main() {
    print_title
    stop_services
    sleep 2
    check_ports
    
    echo ""
    print_message "🎉 所有服务已停止！" $GREEN
    echo ""
}

main
