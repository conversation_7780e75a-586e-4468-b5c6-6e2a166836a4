#!/bin/bash

# Coser场照分析器 - 快速启动脚本
# 简化版本，更可靠的启动方式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_message() {
    echo -e "${2}${1}${NC}"
}

print_title() {
    echo ""
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}🎭 Coser场照分析器 - 快速启动${NC}"
    echo -e "${PURPLE}================================${NC}"
    echo ""
}

# 设置代理
setup_proxy() {
    export http_proxy=http://127.0.0.1:7890
    export https_proxy=http://127.0.0.1:7890
    print_message "🌐 代理已设置" $GREEN
}

# 启动后端
start_backend() {
    print_message "🚀 启动后端服务..." $BLUE
    
    cd backend
    
    # 检查虚拟环境
    if [ ! -d "coser_analyzer_env" ]; then
        print_message "❌ 虚拟环境不存在，请先运行 ./install.sh" $RED
        exit 1
    fi
    
    # 启动后端
    print_message "🔄 启动FastAPI服务器..." $CYAN
    source coser_analyzer_env/bin/activate
    
    # 后台启动并保存PID
    nohup python -m app.main > ../backend.log 2>&1 &
    echo $! > ../backend.pid
    
    # 等待启动
    print_message "⏳ 等待后端启动..." $YELLOW
    sleep 5
    
    # 检查是否启动成功
    if curl -s --connect-timeout 3 http://localhost:8000/api/v1/health > /dev/null 2>&1; then
        print_message "✅ 后端服务启动成功" $GREEN
        print_message "   📖 API文档: http://localhost:8000/docs" $NC
    else
        print_message "❌ 后端启动失败，查看日志: backend.log" $RED
        exit 1
    fi
    
    cd ..
}

# 启动前端
start_frontend() {
    print_message "🎨 启动前端服务..." $BLUE
    
    cd frontend
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        print_message "❌ 前端依赖未安装，请先运行 ./install.sh" $RED
        exit 1
    fi
    
    # 设置Node.js环境
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    nvm use v20.19.0 > /dev/null 2>&1
    
    # 启动前端
    print_message "🔄 启动Vite开发服务器..." $CYAN
    nohup npm run dev > ../frontend.log 2>&1 &
    echo $! > ../frontend.pid
    
    # 等待启动
    print_message "⏳ 等待前端启动..." $YELLOW
    sleep 8
    
    # 检查是否启动成功
    if curl -s --connect-timeout 3 http://localhost:3000 > /dev/null 2>&1; then
        print_message "✅ 前端服务启动成功" $GREEN
        print_message "   🌐 前端应用: http://localhost:3000" $NC
    else
        print_message "❌ 前端启动失败，查看日志: frontend.log" $RED
        exit 1
    fi
    
    cd ..
}

# 打开浏览器
open_browser() {
    print_message "🌐 打开浏览器..." $BLUE
    sleep 2
    
    if command -v open > /dev/null 2>&1; then
        open http://localhost:3000
        print_message "✅ 浏览器已打开" $GREEN
    else
        print_message "请手动打开: http://localhost:3000" $YELLOW
    fi
}

# 显示信息
show_info() {
    echo ""
    print_message "🎉 启动完成！" $GREEN
    echo ""
    print_message "📊 服务地址:" $CYAN
    print_message "  • 前端应用: http://localhost:3000" $NC
    print_message "  • 后端API: http://localhost:8000" $NC
    print_message "  • API文档: http://localhost:8000/docs" $NC
    echo ""
    print_message "📝 日志文件:" $CYAN
    print_message "  • 后端日志: backend.log" $NC
    print_message "  • 前端日志: frontend.log" $NC
    echo ""
    print_message "🛑 停止服务: ./stop.sh" $CYAN
    echo ""
}

# 检查服务状态
check_services() {
    print_message "🔍 检查现有服务..." $BLUE
    
    # 检查后端
    if curl -s --connect-timeout 2 http://localhost:8000/api/v1/health > /dev/null 2>&1; then
        print_message "⚠️  后端服务已在运行，跳过启动" $YELLOW
        BACKEND_RUNNING=true
    else
        BACKEND_RUNNING=false
    fi
    
    # 检查前端
    if curl -s --connect-timeout 2 http://localhost:3000 > /dev/null 2>&1; then
        print_message "⚠️  前端服务已在运行，跳过启动" $YELLOW
        FRONTEND_RUNNING=true
    else
        FRONTEND_RUNNING=false
    fi
}

# 主函数
main() {
    print_title
    setup_proxy
    check_services
    
    # 根据状态启动服务
    if [ "$BACKEND_RUNNING" = false ]; then
        start_backend
    fi
    
    if [ "$FRONTEND_RUNNING" = false ]; then
        start_frontend
    fi
    
    open_browser
    show_info
}

# 运行主函数
main
